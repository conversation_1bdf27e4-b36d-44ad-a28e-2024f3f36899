---
description: Explore detailed documentation of block modules in Ultralytics, available for deep learning tasks. Contribute and improve the codebase!.
keywords: Ultralytics, YOLO, neural networks, block modules, DFL, Proto, HGStem, HGBlock, SPP, SPPF, C1, C2, C2f, C3, C3x, RepC3, C3TR, C3Ghost, GhostBottleneck, Bottleneck, BottleneckCSP, ResNetBlock, MaxSigmoidAttnBlock, ImagePoolingAttn, ContrastiveHead, RepBottleneck, RepCSP, RepNCSPELAN4, ADown, SPPELAN, Silence, CBLinear, CBFuse
---

# Reference for `ultralytics/nn/modules/block.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/modules/block.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/nn/modules/block.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/nn/modules/block.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.nn.modules.block.DFL

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.Proto

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.HGStem

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.HGBlock

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.SPP

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.SPPF

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.C1

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.C2

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.C2f

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.C3

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.C3x

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.RepC3

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.C3TR

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.C3Ghost

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.GhostBottleneck

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.Bottleneck

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.BottleneckCSP

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.ResNetBlock

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.ResNetLayer

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.MaxSigmoidAttnBlock

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.C2fAttn

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.ImagePoolingAttn

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.ContrastiveHead

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.BNContrastiveHead

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.RepBottleneck

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.RepCSP

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.RepNCSPELAN4

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.ELAN1

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.AConv

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.ADown

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.SPPELAN

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.CBLinear

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.CBFuse

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.RepVGGDW

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.CIB

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.C2fCIB

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.Attention

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.PSA

<br><br><hr><br>

## ::: ultralytics.nn.modules.block.SCDown

<br><br>
