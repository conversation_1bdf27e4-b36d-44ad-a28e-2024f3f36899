---
comments: true
description: Explore various utility functions in ultralytics.data.explorer.utils including schema definitions, batch sanitization, and query results plotting.
keywords: Ultralytics, data explorer, utils, schema, sanitize batch, plot query results, SQL query, machine learning
---

# Reference for `ultralytics/data/explorer/utils.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/explorer/utils.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/explorer/utils.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/explorer/utils.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.data.explorer.utils.get_table_schema

<br><br><hr><br>

## ::: ultralytics.data.explorer.utils.get_sim_index_schema

<br><br><hr><br>

## ::: ultralytics.data.explorer.utils.sanitize_batch

<br><br><hr><br>

## ::: ultralytics.data.explorer.utils.plot_query_result

<br><br><hr><br>

## ::: ultralytics.data.explorer.utils.prompt_sql_query

<br><br>
