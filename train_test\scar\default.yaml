# Ultralytics YOLO 🚀, AGPL-3.0 license
# 默认训练设置和超参数配置(适用于中等数据增强的COCO数据集训练)

# 基础任务配置
task: detect  # 任务类型: detect(目标检测)、segment(分割)、classify(分类)、pose(姿态估计)
mode: train   # 运行模式: train(训练)、val(验证)、predict(预测)、export(导出)、track(跟踪)、benchmark(基准)

# 训练设置 -------------------------------------------------------------------------------------------------------
model: yolov8n.yaml  # 模型配置文件路径,如果继续训练则指定模型路径,重新训练则指定配置文件
data: yolo8_scar.yaml  # 数据集配置文件路径
epochs: 100  # 训练轮数
patience: 100  # 早停耐心值,当模型性能不再提升时等待的轮数
batch: 16  # 每批次处理的图像数量
imgsz: 640  # 输入图像大小
save: True  # 是否保存训练检查点和预测结果
save_period: -1  # 每隔多少轮保存一次检查点
cache: False  # 数据加载缓存设置
device: # 训练设备,可以是GPU编号或CPU
workers: 1  # 数据加载的工作进程数
project:  # 项目名称
name:  # 实验名称,结果将保存在project/name目录
exist_ok: False  # 是否允许覆盖已存在的实验目录
pretrained: True  # 是否使用预训练模型
optimizer: auto  # 优化器选择
verbose: True  # 是否显示详细信息
seed: 0  # 随机种子,用于复现结果
deterministic: True  # 是否启用确定性模式
single_cls: False  # 是否将多类别数据作为单类别处理
rect: False  # 是否使用矩形训练
cos_lr: False  # 是否使用余弦学习率调度
close_mosaic: 10  # 最后多少轮关闭马赛克增强
resume: False  # 是否从断点继续训练
amp: True  # 是否使用自动混合精度训练
fraction: 1.0  # 训练数据集使用比例
profile: False  # 是否在训练时分析ONNX和TensorRT速度
freeze: None  # 冻结前n层或指定层
multi_scale: False  # 是否使用多尺度训练

# 分割任务设置 ----------------------------------------------------------------------------------------------------
overlap_mask: True  # 训练时是否允许掩码重叠
mask_ratio: 4  # 掩码下采样比例

# 分类任务设置 ----------------------------------------------------------------------------------------------------
dropout: 0.0  # dropout正则化比例

# 验证/测试设置 ----------------------------------------------------------------------------------------------------
val: True  # 是否在训练时进行验证
split: val  # 验证集划分方式
save_json: False  # 是否保存JSON格式结果
save_hybrid: False  # 是否保存混合版本标签
conf:  # 目标检测置信度阈值
iou: 0.7  # NMS的IOU阈值
max_det: 300  # 每张图像最大检测框数量
half: False  # 是否使用半精度(FP16)
dnn: False  # 是否使用OpenCV DNN进行ONNX推理
plots: True  # 是否保存训练/验证过程的图表

# 预测设置 -----------------------------------------------------------------------------------------------------
source:  # 预测源(图片/视频目录)
vid_stride: 1  # 视频帧率步长
stream_buffer: False  # 是否缓冲所有流式帧
visualize: False  # 是否可视化模型特征
augment: False  # 是否对预测源进行图像增强
agnostic_nms: False  # 是否使用类别无关的NMS
classes:  # 按类别过滤结果
retina_masks: False  # 是否使用高分辨率分割掩码
embed:  # 返回指定层的特征向量

# 可视化设置 ---------------------------------------------------------------------------------------------------
show: False  # 是否显示预测结果
save_frames: False  # 是否保存预测的视频帧
save_txt: False  # 是否保存txt格式结果
save_conf: False  # 是否保存置信度分数
save_crop: False  # 是否保存裁剪后的目标图像
show_labels: True  # 是否显示预测标签
show_conf: True  # 是否显示预测置信度
show_boxes: True  # 是否显示预测框
line_width:  # 边界框线宽

# 导出设置 ------------------------------------------------------------------------------------------------------
format: rknn  # 导出格式
keras: False  # 是否使用Keras
optimize: False  # 是否优化TorchScript模型
int8: False  # 是否进行INT8量化
dynamic: False  # 是否使用动态轴
simplify: False  # 是否简化ONNX模型
opset: 12 # ONNX算子集版本
workspace: 4  # TensorRT工作空间大小(GB)
nms: False  # 是否添加NMS

# 超参数设置 ------------------------------------------------------------------------------------------------------
lr0: 0.01  # 初始学习率
lrf: 0.01  # 最终学习率(初始学习率 * lrf)
momentum: 0.937  # SGD动量/Adam beta1
weight_decay: 0.0005  # 权重衰减
warmup_epochs: 3.0  # 预热轮数
warmup_momentum: 0.8  # 预热初始动量
warmup_bias_lr: 0.1  # 预热初始偏置学习率
box: 7.5  # 框损失权重
cls: 0.5  # 分类损失权重
dfl: 1.5  # DFL损失权重
pose: 12.0  # 姿态损失权重
kobj: 1.0  # 关键点目标损失权重
label_smoothing: 0.0  # 标签平滑
nbs: 64  # 标称批次大小

# 数据增强参数 ------------------------------------------------------------------------------------------------------
hsv_h: 0.015  # HSV色调增强
hsv_s: 0.7  # HSV饱和度增强
hsv_v: 0.4  # HSV亮度增强
degrees: 0.0  # 旋转角度
translate: 0.1  # 平移
scale: 0.5  # 缩放
shear: 0.0  # 剪切
perspective: 0.0  # 透视变换
flipud: 0.0  # 上下翻转概率
fliplr: 0.5  # 左右翻转概率
bgr: 0.0  # BGR通道概率
mosaic: 1.0  # 马赛克增强概率
mixup: 0.0  # mixup增强概率
copy_paste: 0.0  # 复制粘贴概率
auto_augment: randaugment  # 自动增强策略
erasing: 0.4  # 随机擦除概率
crop_fraction: 1.0  # 裁剪比例

# 自定义配置 ------------------------------------------------------------------------------------------------------
cfg:  # 用于覆盖默认配置

# 跟踪器设置 ------------------------------------------------------------------------------------------------------
tracker: botsort.yaml  # 跟踪器类型
