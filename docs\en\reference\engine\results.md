---
description: Explore the details of Ultralytics engine results including classes like BaseTensor, Results, Boxes, Masks, Keypoints, Probs, and OBB to handle inference results efficiently.
keywords: Ultralytics, engine results, BaseTensor, Results class, Boxes, Masks, Keypoints, Probs, OBB, inference results, machine learning, PyTorch
---

# Reference for `ultralytics/engine/results.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/results.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/engine/results.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/engine/results.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.engine.results.BaseTensor

<br><br><hr><br>

## ::: ultralytics.engine.results.Results

<br><br><hr><br>

## ::: ultralytics.engine.results.Boxes

<br><br><hr><br>

## ::: ultralytics.engine.results.Masks

<br><br><hr><br>

## ::: ultralytics.engine.results.Keypoints

<br><br><hr><br>

## ::: ultralytics.engine.results.Probs

<br><br><hr><br>

## ::: ultralytics.engine.results.OBB

<br><br>
