---
description: Explore the Analytics class in Ultralytics for visual analytics. Learn to create and update line, bar, and pie charts efficiently.
keywords: Ultralytics, Analytics, Python, visual analytics, line chart, bar chart, pie chart, data visualization, AGPL-3.0 license
---

# Reference for `ultralytics/solutions/analytics.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/analytics.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/analytics.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/analytics.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.solutions.analytics.Analytics

<br><br>
