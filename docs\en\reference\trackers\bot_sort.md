---
description: Explore the robust object tracking capabilities of the BOTrack and BOTSORT classes in the Ultralytics Bot SORT tracker API. Enhance your YOLOv8 projects.
keywords: Ultralytics, Bot SORT, BOTrack, BOTSORT, YOLOv8, object tracking, <PERSON><PERSON> filter, ReID, GMC algorithm
---

# Reference for `ultralytics/trackers/bot_sort.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/bot_sort.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/bot_sort.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/bot_sort.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.trackers.bot_sort.BOTrack

<br><br><hr><br>

## ::: ultralytics.trackers.bot_sort.BOTSORT

<br><br>
