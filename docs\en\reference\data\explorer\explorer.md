---
comments: true
description: Explore the Ultralytics data explorer functions including YOLO dataset handling, image querying, embedding generation, and similarity indexing.
keywords: Ultralytics, YOLO, data explorer, image querying, embeddings, similarity index, python, machine learning
---

# Reference for `ultralytics/data/explorer/explorer.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/explorer/explorer.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/explorer/explorer.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/explorer/explorer.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.data.explorer.explorer.ExplorerDataset

<br><br><hr><br>

## ::: ultralytics.data.explorer.explorer.Explorer

<br><br>
