---
description: Explore the train.py module in Ultralytics YOLO for efficient classification model training. Learn more with examples and detailed code documentation.
keywords: YOLO, Ultralytics, classification, training, machine learning, deep learning, PyTorch, train.py
---

# Reference for `ultralytics/models/yolo/classify/train.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/classify/train.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/classify/train.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/classify/train.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.classify.train.ClassificationTrainer

<br><br>
