---
description: Explore the Ultralytics BaseDataset class for efficient image loading and processing with custom transformations and caching options.
keywords: Ultralytics, BaseDataset, image processing, data augmentation, YOLO, dataset class, image caching
---

# Reference for `ultralytics/data/base.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/base.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/base.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/base.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.data.base.BaseDataset

<br><br>
