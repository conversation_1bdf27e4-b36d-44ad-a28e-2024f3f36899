{% if page.meta.comments %}
<h2 id="__comments">{{ lang.t("meta.comments") }}</h2>

<!-- Insert Giscus code snippet from https://giscus.app/ here -->
<script async
        crossorigin="anonymous"
        data-category="Docs"
        data-category-id="DIC_kwDOH-jzvc4CWLkL"
        data-emit-metadata="0"
        data-input-position="top"
        data-lang="en"
        data-loading="lazy"
        data-mapping="pathname"
        data-reactions-enabled="1"
        data-repo="ultralytics/ultralytics"
        data-repo-id="R_kgDOH-jzvQ"
        data-strict="1"
        data-theme="preferred_color_scheme"
        src="https://giscus.app/client.js">
</script>

<!-- Synchronize Giscus theme with palette -->
<script>
    var giscus = document.querySelector("script[src*=giscus]")

    /* Set palette on initial load */
    var palette = __md_get("__palette")
    if (palette && typeof palette.color === "object") {
      var theme = palette.color.scheme === "slate" ? "dark" : "light"
      giscus.setAttribute("data-theme", theme)
    }

    /* Register event handlers after documented loaded */
    document.addEventListener("DOMContentLoaded", function() {
      var ref = document.querySelector("[data-md-component=palette]")
      ref.addEventListener("change", function() {
        var palette = __md_get("__palette")
        if (palette && typeof palette.color === "object") {
          var theme = palette.color.scheme === "slate" ? "dark" : "light"

          /* Instruct Giscus to change theme */
          var frame = document.querySelector(".giscus-frame")
          frame.contentWindow.postMessage(
            { giscus: { setConfig: { theme } } },
            "https://giscus.app"
          )
        }
      })
    })
</script>
{% endif %}
