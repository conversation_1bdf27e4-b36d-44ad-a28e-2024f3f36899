from ultralytics import YOLO
import cv2
import time

# 加载YOLOv8模型
model = YOLO('best.pt')  # 使用预训练模型，也可以替换成你自己训练的模型

# 打开摄像头
cap = cv2.VideoCapture(0)  # 0表示默认摄像头，如果有多个摄像头可以尝试1,2等

# 检查摄像头是否成功打开
if not cap.isOpened():
    print("无法打开摄像头")
    exit()

while True:
    # 读取一帧图像
    ret, frame = cap.read()
    
    if not ret:
        print("无法获取画面")
        break
    
    # 使用YOLOv8进行推理
    results = model.predict(frame, conf=0.8)  # conf参数设置置信度阈值
    
    # 在图像上绘制检测结果
    annotated_frame = results[0].plot()
    
    # 显示结果
    cv2.imshow("YOLOv8 Inference", annotated_frame)
    
    # 按'q'键退出
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

# 释放资源
cap.release()
cv2.destroyAllWindows() 