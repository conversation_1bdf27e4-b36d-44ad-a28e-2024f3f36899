# Ultralytics YOLO 🚀, AGPL-3.0 license
# Test and publish docs to https://docs.ultralytics.com
# Ignores the following Docs rules to match Google-style docstrings:
# D100: Missing docstring in public module
# D104: Missing docstring in public package
# D203: 1 blank line required before class docstring
# D205: 1 blank line required between summary line and description
# D212: Multi-line docstring summary should start at the first line
# D213: Multi-line docstring summary should start at the second line
# D401: First line of docstring should be in imperative mood
# D406: Section name should end with a newline
# D407: Missing dashed underline after section
# D413: Missing blank line after last section

name: Publish Docs

on:
  push:
    branches: [main]
  pull_request_target:
    branches: [main]
  workflow_dispatch:

jobs:
  Docs:
    if: github.repository == 'ultralytics/ultralytics'
    runs-on: macos-14
    steps:
      - name: Git config
        run: |
          git config --global user.name "UltralyticsAssistant"
          git config --global user.email "<EMAIL>"
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          repository: ${{ github.event.pull_request.head.repo.full_name || github.repository }}
          token: ${{ secrets.GITHUB_TOKEN }}
          ref: ${{ github.head_ref || github.ref }}
          fetch-depth: 0
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
          cache: "pip" # caching pip dependencies
      - name: Install Dependencies
        run: pip install ruff black tqdm mkdocs-material "mkdocstrings[python]" mkdocs-jupyter mkdocs-redirects mkdocs-ultralytics-plugin mkdocs-macros-plugin
      - name: Ruff fixes
        continue-on-error: true
        run: ruff check --fix --fix-unsafe --select D --ignore=D100,D104,D203,D205,D212,D213,D401,D406,D407,D413 .
      - name: Update Docs Reference Section and Push Changes
        if: github.event_name == 'pull_request_target'
        run: |
          python docs/build_reference.py
          git pull origin ${{ github.head_ref || github.ref }}
          git add .
          git reset HEAD -- .github/workflows/  # workflow changes are not permitted with default token
          if ! git diff --staged --quiet; then
            git commit -m "Auto-update Ultralytics Docs Reference by https://ultralytics.com/actions"
            git push
          else
            echo "No changes to commit"
          fi
      - name: Ruff checks
        run: ruff check --select D --ignore=D100,D104,D203,D205,D212,D213,D401,D406,D407,D413 .
      - name: Build Docs and Check for Warnings
        run: |
          export JUPYTER_PLATFORM_DIRS=1
          python docs/build_docs.py
      - name: Commit and Push Docs changes
        continue-on-error: true
        if: always() && github.event_name == 'pull_request_target'
        run: |
          git pull origin ${{ github.head_ref || github.ref }}
          git add --update  # only add updated files
          git reset HEAD -- .github/workflows/  # workflow changes are not permitted with default token
          if ! git diff --staged --quiet; then
            git commit -m "Auto-update Ultralytics Docs by https://ultralytics.com/actions"
            git push
          else
            echo "No changes to commit"
          fi
      - name: Publish Docs to https://docs.ultralytics.com
        if: github.event_name == 'push'
        env:
          PERSONAL_ACCESS_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          INDEXNOW_KEY: ${{ secrets.INDEXNOW_KEY_DOCS }}
        run: |
          git clone https://github.com/ultralytics/docs.git docs-repo
          cd docs-repo
          git checkout gh-pages || git checkout -b gh-pages
          rm -rf *
          cp -R ../site/* .
          echo "$INDEXNOW_KEY" > "$INDEXNOW_KEY.txt"
          git add .
          if git diff --staged --quiet; then
            echo "No changes to commit"
          else
            LATEST_HASH=$(git rev-parse --short=7 HEAD)
            git commit -m "Update Docs for 'ultralytics ${{ steps.check_pypi.outputs.version }} - $LATEST_HASH'"
            git push https://$<EMAIL>/ultralytics/docs.git gh-pages
          fi
