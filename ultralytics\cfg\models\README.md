## 模型

欢迎来到 [Ultralytics](https://ultralytics.com) 模型目录！在这里您可以找到各种预配置的模型配置文件(`*.yaml`)，这些文件可用于创建自定义的YOLO模型。本目录中的模型由Ultralytics团队精心设计和微调，可为各种目标检测和图像分割任务提供最佳性能。

这些模型配置涵盖了从简单的目标检测到更复杂的实例分割和目标跟踪等广泛场景。它们还被设计为可以在从CPU到GPU的各种硬件平台上高效运行。无论您是经验丰富的机器学习从业者还是刚开始使用YOLO，本目录都能为您的自定义模型开发需求提供一个很好的起点。

要开始使用，只需浏览本目录中的模型并找到最适合您需求的模型。选择模型后，您可以使用提供的`*.yaml`文件轻松训练和部署您的自定义YOLO模型。完整详情请参见Ultralytics [文档](https://docs.ultralytics.com/models)。如果您需要帮助或有任何问题，请随时联系Ultralytics团队获取支持。所以，不要等待，现在就开始创建您的自定义YOLO模型吧！

### 使用方法

模型`*.yaml`文件可以直接在[命令行界面(CLI)](https://docs.ultralytics.com/usage/cli)中使用`yolo`命令：

```bash
# 使用coco8数据集训练YOLOv8n模型100轮
yolo task=detect mode=train model=yolov8n.yaml data=coco8.yaml epochs=100
```

也可以在Python环境中直接使用，并接受与上述CLI示例相同的[参数](https://docs.ultralytics.com/usage/cfg/)：

```python
from ultralytics import YOLO

# 从YAML配置文件初始化YOLOv8n模型
model = YOLO("model.yaml")

# 如果有预训练模型可用，则使用它
# model = YOLO("model.pt")

# 显示模型信息
model.info()

# 使用COCO8数据集训练模型100轮
model.train(data="coco8.yaml", epochs=100)
```

## 预训练模型架构

Ultralytics支持多种模型架构。访问[Ultralytics模型](https://docs.ultralytics.com/models)查看详细信息和用法。如果有可用的配置或预训练检查点，可以使用这些模型中的任何一个。

## 贡献新模型

您是否训练了新的YOLO变体或通过特定调优实现了最先进的性能？我们很乐意在我们的模型部分展示您的工作！来自社区的新模型、架构或优化形式的贡献非常受重视，可以极大地丰富我们的仓库。

通过为本部分做出贡献，您正在帮助我们为社区提供更广泛的模型选择和配置。这是分享您的知识和专业知识的绝佳方式，同时使Ultralytics YOLO生态系统更加多样化。

要开始，请查看我们的[贡献指南](https://docs.ultralytics.com/help/contributing)，了解如何提交拉取请求(PR)的详细步骤 🛠️。我们期待您的贡献！

让我们携手扩展Ultralytics YOLO模型的范围和功能 🙏！
