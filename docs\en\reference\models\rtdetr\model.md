---
description: Explore the interface for <PERSON><PERSON>'s RT-DETR, a Vision Transformer-based real-time object detector in the Ultralytics Docs. Learn more about its efficient hybrid encoding and IoU-aware query selection.
keywords: RT-DETR, real-time object detection, Vision Transformer, Ultralytics, model interface, Baidu, hybrid encoding, IoU-aware query selection, machine learning, AI
---

# Reference for `ultralytics/models/rtdetr/model.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/rtdetr/model.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/rtdetr/model.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/rtdetr/model.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.rtdetr.model.RTDETR

<br><br>
