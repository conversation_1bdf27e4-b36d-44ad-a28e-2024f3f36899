---
comments: true
description: Optimize parking spaces and enhance safety with Ultralytics YOLOv8. Explore real-time vehicle detection and smart parking solutions.
keywords: parking management, YOLOv8, Ultralytics, vehicle detection, real-time tracking, parking lot optimization, smart parking
---

# Parking Management using Ultralytics YOLOv8 🚀

## What is Parking Management System?

Parking management with [Ultralytics YOLOv8](https://github.com/ultralytics/ultralytics/) ensures efficient and safe parking by organizing spaces and monitoring availability. YOLOv8 can improve parking lot management through real-time vehicle detection, and insights into parking occupancy.

<p align="center">
  <br>
  <iframe loading="lazy" width="720" height="405" src="https://www.youtube.com/embed/WwXnljc7ZUM"
    title="YouTube video player" frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
    allowfullscreen>
  </iframe>
  <br>
  <strong>Watch:</strong> How to Implement Parking Management Using Ultralytics YOLOv8 🚀
</p>

## Advantages of Parking Management System?

- **Efficiency**: Parking lot management optimizes the use of parking spaces and reduces congestion.
- **Safety and Security**: Parking management using YOLOv8 improves the safety of both people and vehicles through surveillance and security measures.
- **Reduced Emissions**: Parking management using YOLOv8 manages traffic flow to minimize idle time and emissions in parking lots.

## Real World Applications

|                                                                Parking Management System                                                                |                                                                  Parking Management System                                                                   |
| :-----------------------------------------------------------------------------------------------------------------------------------------------------: | :----------------------------------------------------------------------------------------------------------------------------------------------------------: |
| ![Parking lots Analytics Using Ultralytics YOLOv8](https://github.com/RizwanMunawar/RizwanMunawar/assets/62513924/e3d4bc3e-cf4a-4da9-b42e-0da55cc74ad6) | ![Parking management top view using Ultralytics YOLOv8](https://github.com/RizwanMunawar/RizwanMunawar/assets/62513924/fe186719-1aca-43c9-b388-1ded91280eb5) |
|                                                 Parking management Aerial View using Ultralytics YOLOv8                                                 |                                                     Parking management Top View using Ultralytics YOLOv8                                                     |

## Parking Management System Code Workflow

### Selection of Points

!!! Tip "Point Selection is now Easy"

    Choosing parking points is a critical and complex task in parking management systems. Ultralytics streamlines this process by providing a tool that lets you define parking lot areas, which can be utilized later for additional processing.

- Capture a frame from the video or camera stream where you want to manage the parking lot.
- Use the provided code to launch a graphical interface, where you can select an image and start outlining parking regions by mouse click to create polygons.

!!! Warning "Image Size"

    Max Image Size of 1920 * 1080 supported

!!! Example "Parking slots Annotator Ultralytics YOLOv8"

    === "Parking Annotator"

        ```python
        from ultralytics import solutions

        solutions.ParkingPtsSelection()
        ```

- After defining the parking areas with polygons, click `save` to store a JSON file with the data in your working directory.

![Ultralytics YOLOv8 Points Selection Demo](https://github.com/RizwanMunawar/RizwanMunawar/assets/62513924/72737b8a-0f0f-4efb-98ad-b917a0039535)

### Python Code for Parking Management

!!! Example "Parking management using YOLOv8 Example"

    === "Parking Management"

        ```python
        import cv2

        from ultralytics import solutions

        # Path to json file, that created with above point selection app
        polygon_json_path = "bounding_boxes.json"

        # Video capture
        cap = cv2.VideoCapture("Path/to/video/file.mp4")
        assert cap.isOpened(), "Error reading video file"
        w, h, fps = (int(cap.get(x)) for x in (cv2.CAP_PROP_FRAME_WIDTH, cv2.CAP_PROP_FRAME_HEIGHT, cv2.CAP_PROP_FPS))

        # Video writer
        video_writer = cv2.VideoWriter("parking management.avi", cv2.VideoWriter_fourcc(*"mp4v"), fps, (w, h))

        # Initialize parking management object
        management = solutions.ParkingManagement(model_path="yolov8n.pt")

        while cap.isOpened():
            ret, im0 = cap.read()
            if not ret:
                break

            json_data = management.parking_regions_extraction(polygon_json_path)
            results = management.model.track(im0, persist=True, show=False)

            if results[0].boxes.id is not None:
                boxes = results[0].boxes.xyxy.cpu().tolist()
                clss = results[0].boxes.cls.cpu().tolist()
                management.process_data(json_data, im0, boxes, clss)

            management.display_frames(im0)
            video_writer.write(im0)

        cap.release()
        video_writer.release()
        cv2.destroyAllWindows()
        ```

### Optional Arguments `ParkingManagement`

| Name                     | Type    | Default           | Description                            |
| ------------------------ | ------- | ----------------- | -------------------------------------- |
| `model_path`             | `str`   | `None`            | Path to the YOLOv8 model.              |
| `txt_color`              | `tuple` | `(0, 0, 0)`       | RGB color tuple for text.              |
| `bg_color`               | `tuple` | `(255, 255, 255)` | RGB color tuple for background.        |
| `occupied_region_color`  | `tuple` | `(0, 255, 0)`     | RGB color tuple for occupied regions.  |
| `available_region_color` | `tuple` | `(0, 0, 255)`     | RGB color tuple for available regions. |
| `margin`                 | `int`   | `10`              | Margin for text display.               |

### Arguments `model.track`

| Name      | Type    | Default        | Description                                                 |
| --------- | ------- | -------------- | ----------------------------------------------------------- |
| `source`  | `im0`   | `None`         | source directory for images or videos                       |
| `persist` | `bool`  | `False`        | persisting tracks between frames                            |
| `tracker` | `str`   | `botsort.yaml` | Tracking method 'bytetrack' or 'botsort'                    |
| `conf`    | `float` | `0.3`          | Confidence Threshold                                        |
| `iou`     | `float` | `0.5`          | IOU Threshold                                               |
| `classes` | `list`  | `None`         | filter results by class, i.e. classes=0, or classes=[0,2,3] |
| `verbose` | `bool`  | `True`         | Display the object tracking results                         |

## FAQ

### How does Ultralytics YOLOv8 enhance parking management systems?

Ultralytics YOLOv8 greatly enhances parking management systems by providing **real-time vehicle detection** and monitoring. This results in optimized usage of parking spaces, reduced congestion, and improved safety through continuous surveillance. The [Parking Management System](https://github.com/ultralytics/ultralytics) enables efficient traffic flow, minimizing idle times and emissions in parking lots, thereby contributing to environmental sustainability. For further details, refer to the [parking management code workflow](#python-code-for-parking-management).

### What are the benefits of using Ultralytics YOLOv8 for smart parking?

Using Ultralytics YOLOv8 for smart parking yields numerous benefits:

- **Efficiency**: Optimizes the use of parking spaces and decreases congestion.
- **Safety and Security**: Enhances surveillance and ensures the safety of vehicles and pedestrians.
- **Environmental Impact**: Helps in reducing emissions by minimizing vehicle idle times. More details on the advantages can be seen [here](#advantages-of-parking-management-system).

### How can I define parking spaces using Ultralytics YOLOv8?

Defining parking spaces is straightforward with Ultralytics YOLOv8:

1. Capture a frame from a video or camera stream.
2. Use the provided code to launch a GUI for selecting an image and drawing polygons to define parking spaces.
3. Save the labeled data in JSON format for further processing. For comprehensive instructions, check the [selection of points](#selection-of-points) section.

### Can I customize the YOLOv8 model for specific parking management needs?

Yes, Ultralytics YOLOv8 allows customization for specific parking management needs. You can adjust parameters such as the **occupied and available region colors**, margins for text display, and much more. Utilizing the `ParkingManagement` class's [optional arguments](#optional-arguments-parkingmanagement), you can tailor the model to suit your particular requirements, ensuring maximum efficiency and effectiveness.

### What are some real-world applications of Ultralytics YOLOv8 in parking lot management?

Ultralytics YOLOv8 is utilized in various real-world applications for parking lot management, including:

- **Parking Space Detection**: Accurately identifying available and occupied spaces.
- **Surveillance**: Enhancing security through real-time monitoring.
- **Traffic Flow Management**: Reducing idle times and congestion with efficient traffic handling. Images showcasing these applications can be found in [real-world applications](#real-world-applications).
