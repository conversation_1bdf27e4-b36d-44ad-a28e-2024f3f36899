---
description: Explore Ultralytics' annotator script for automatic image annotation using YOLO and SAM models. Contribute to improve it on GitHub!.
keywords: Ultralytics, image annotation, YOLO, SAM, Python script, GitHub, object detection, segmentation
---

# Reference for `ultralytics/data/annotator.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/annotator.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/annotator.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/annotator.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.data.annotator.auto_annotate

<br><br>
